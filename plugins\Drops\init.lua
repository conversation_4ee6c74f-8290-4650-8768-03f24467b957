local json_reader = require "../utils/json_reader"
local matcher = require('../core/matcher')
local log = require "../core/log"
local process_data = require('../plugins/Drops/process_data')
local fzy = require('../plugins/Drops/fzy')
local json = require "json"



--------
local cached_drops_zh = nil
local cached_drops_en = nil
local cached_translations = nil

-- 初始化缓存
local function init_cache()
    if cached_drops_zh then return end

    json_reader.read_async("assets/json/warframe_drops_list.json",
                           function(err, data)
        if err then return log.error("读取掉落数据失败:", err) end

        process_data.process_all_drops(data,
                                       function(process_err, zh_data, en_data)
            if process_err then
                return log.error("处理数据失败:", process_err)
            end
            cached_drops_zh = zh_data
            cached_drops_en = en_data

            -- -- 保存中文字典到本地
            -- local fs = require('fs')
            -- fs.writeFile("debug_zh_dict.json", json.encode(zh_data),
            --              function(write_err)
            --     if write_err then
            --         log.error("保存中文字典失败:", write_err)
            --     else
            --         log.info("中文字典已保存到 debug_zh_dict.json")
            --     end
            -- end)

            -- 加载翻译字典
            json_reader.read_async("assets/json/wiki_translations.json",
                                   function(trans_err, trans_data)
                if not trans_err then
                    cached_translations = trans_data.Text or {}
                end
            end)
        end)
    end)
end

-- 模糊搜索
local function search(query)
    if not cached_drops_zh then return {} end

    -- 根据输入语言选择候选表
    local candidates = {}
    local target_table = process_data.has_chinese(query) and cached_drops_zh or
                             cached_drops_en

    if target_table then
        for name, _ in pairs(target_table) do
            table.insert(candidates, name)
        end
    end

    local matches = fzy.filter(query, candidates, false)
    table.sort(matches, function(a, b) return a[3] > b[3] end)

    local results = {}
    for i = 1, math.min(5, #matches) do
        local name = candidates[matches[i][1]]
        local drops = target_table and target_table[name]
        table.insert(results, {name = name, drops = drops})
    end

    return results
end


-- 格式化输出
local function format(name, drops)
    local translated_drops = process_data.translate_drops(drops,cached_translations)

    -- 按掉率排序后取前10个
    table.sort(translated_drops, function(a, b)
        local chance_a = tonumber(a:match("([%d%.]+)%%")) or 0
        local chance_b = tonumber(b:match("([%d%.]+)%%")) or 0
        return chance_a > chance_b
    end)

    local limited_drops = {}
    for i = 1, math.min(10, #translated_drops) do
        table.insert(limited_drops, translated_drops[i])
    end

    local msg = name .. "：\n"
    for i = 1, math.min(5, #limited_drops) do
        msg = msg .. "• " .. limited_drops[i] .. "\n"
    end
    if #limited_drops > 5 then msg = msg .. "..." end
    return msg
end

matcher.on_command("掉落", function(bot, event)
    local query = event.args or ""
    if query == "" then
        return bot:send_group_msg(event.group_id, "请输入物品名称")
    end

    if not cached_drops_zh then
        return bot:send_group_msg(event.group_id, "数据加载中...")
    end

    -- 精确匹配（根据语言选择表）
    local target_table = process_data.has_chinese(query) and cached_drops_zh or
                             cached_drops_en
    local drops = target_table and target_table[query]
    if drops then
        return bot:send_group_msg(event.group_id, format(query, drops))
    end

    -- 模糊匹配
    local results = search(query)
    if #results == 0 then
        return bot:send_group_msg(event.group_id, "未找到相关物品")
    end

    if #results == 1 then
        local r = results[1]
        return bot:send_group_msg(event.group_id, format(r.name, r.drops))
    end

    -- 多个结果
    local msg = "找到多个物品：\n"
    for _, r in ipairs(results) do msg = msg .. r.name .. "\n" end
    bot:send_group_msg(event.group_id, msg)
end)

init_cache()
