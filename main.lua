-- 镜像编译MoonScript文件（启动时执行一次）
local function compile_moonscript()
    local fs = require('fs')
    local os = require('os')
    
    local function ensure_dir(dir_path)
        pcall(fs.mkdirSync, dir_path)
    end
    
    local function compile_directory(moon_dir, lua_dir)
        local ok, files = pcall(fs.readdirSync, moon_dir)
        if not ok then return end
        
        ensure_dir(lua_dir)
        
        for _, file in ipairs(files) do
            local moon_path = moon_dir .. "/" .. file
            local stat = fs.statSync(moon_path)
            
            if stat.type == "file" and file:match("%.moon$") then
                local lua_file = file:gsub("%.moon$", ".lua")
                local lua_path = lua_dir .. "/" .. lua_file
                
                local moon_stat = stat
                local lua_stat = pcall(fs.statSync, lua_path) and fs.statSync(lua_path) or nil
                
                if not lua_stat or moon_stat.mtime.sec > lua_stat.mtime.sec then
                    print("编译 " .. moon_path .. " -> " .. lua_path)
                    os.execute("moonc -o " .. lua_dir .. " " .. moon_path)
                end
            elseif stat.type == "directory" then
                compile_directory(moon_path, lua_dir .. "/" .. file)
            end
        end
    end
    
    compile_directory("moon/core", "core")
    compile_directory("moon/plugins", "plugins")
end

-- 启动时编译
compile_moonscript()

-- 正常启动流程
local log = require('./core/log')
local server = require('./core/ws-server')
local dispatcher = require('./core/dispatcher')

local port = 33894 -- OneBot 客户端连接的端口

dispatcher.load_plugins()
server.start(port)

log.info("✨ OneBot-Luvit · Laobot 引擎点火，星河就绪")

