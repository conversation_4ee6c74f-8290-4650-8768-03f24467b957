local fs = require('fs')
local json = require('json')

local json_reader = {}

-- 异步读取JSON文件
function json_reader.read_async(file_path, callback)
    fs.readFile(file_path, function(err, data)
        if err then
            callback(err, nil)
            return
        end
        
        local ok, result = pcall(json.decode, data)
        if ok then
            callback(nil, result)
        else
            callback("JSON解析失败: " .. tostring(result), nil)
        end
    end)
end

-- 同步读取JSON文件
function json_reader.read_sync(file_path)
    local ok, data = pcall(fs.readFileSync, file_path)
    if not ok then
        return nil, "文件读取失败: " .. tostring(data)
    end
    
    local parse_ok, result = pcall(json.decode, data)
    if parse_ok then
        return result, nil
    else
        return nil, "JSON解析失败: " .. tostring(result)
    end
end

return json_reader