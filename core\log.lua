local log = {}

log.usecolor = true
log.outfile = nil
log.level = "trace"
log.file_name = true


local modes = {
    { name = "trace", color = "\27[34m", },
    { name = "debug", color = "\27[36m", },
    { name = "info",  color = "\27[32m", },
    { name = "warn",  color = "\27[33m", },
    { name = "error", color = "\27[31m", },
    { name = "fatal", color = "\27[35m", },
}


local levels = {}
for i, v in ipairs(modes) do
    levels[v.name] = i
end



local round = function(x, increment)
    increment = increment or 1
    x = x / increment
    return (x > 0 and math.floor(x + .5) or math.ceil(x - .5)) * increment
end


local _tostring = tostring

local tostring = function(...)
    local t = {}
    for i = 1, select('#', ...) do
        local x = select(i, ...)
        if type(x) == "number" then
            x = round(x, .01)
        end
        t[#t + 1] = _tostring(x)
    end
    return table.concat(t, " ")
end


for i, x in ipairs(modes) do
    local name = x.name
    log[name] = function(...)
        if i < levels[log.level] then
            return
        end

        local msg = tostring(...)
        local info = debug.getinfo(2, "Sl")
        local lineinfo = log.file_name and 
            (info.short_src:match("([^/\\]+)$") .. ":" .. info.currentline) or
            (info.short_src:gsub("\\", "/") .. ":" .. info.currentline)

        print(string.format("%s[%-6s%s]%s \27[33m%s\27[0m: %s",
            log.usecolor and x.color or "",
            name:upper(),
            os.date("%H:%M:%S"),
            log.usecolor and "\27[0m" or "",
            lineinfo,
            msg))

        if log.outfile then
            local fp = io.open(log.outfile, "a")
            local str = string.format("[%-6s%s] %s: %s\n",
                name:upper(), os.date(), lineinfo, msg)
            fp:write(str)
            fp:close()
        end
    end
end


return log
