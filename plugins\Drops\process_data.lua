local json_reader = require('../../utils/json_reader')

local M = {}

-- 检测是否包含中文字符
function M.has_chinese(str)
    return str:match("[\228-\233][\128-\191][\128-\191]") ~= nil
end
-- 翻译掉落信息
function M.translate_drops(drops, cached_translations)
    if not cached_translations then return drops end

    -- 稀有度翻译映射
    local rarity_map = {
        ["Common"] = "常见-铜",
        ["Uncommon"] = "罕见-银",
        ["Rare"] = "稀有-金",
        ["Legendary"] = "传说-白金",
        ["Unknown"] = "未知"
    }

    -- 遗物状态翻译映射
    local relic_state_map = {
        ["Intact"] = "完整",
        ["Exceptional"] = "优良",
        ["Flawless"] = "无暇",
        ["Radiant"] = "光辉"
    }

    local translated = {}
    for _, drop_info in ipairs(drops) do
        local translated_info = drop_info

        -- 分割获取敌人名称部分
        local enemy_name = drop_info:match("^([^|]+)")
        if enemy_name then
            enemy_name = enemy_name:gsub("^%s+", ""):gsub("%s+$", "") -- 去除首尾空格

            -- 先尝试完整翻译敌人名称
            if cached_translations[enemy_name] then
                translated_info = drop_info:gsub(enemy_name,
                                                 cached_translations[enemy_name])
            else
                -- 没有完整匹配，再逐词翻译
                for en, zh in pairs(cached_translations) do
                    translated_info = translated_info:gsub(en, zh)
                end
            end
        end

        -- 翻译稀有度
        for en, zh in pairs(rarity_map) do
            translated_info = translated_info:gsub(en, zh)
        end

        -- 翻译遗物状态
        for en, zh in pairs(relic_state_map) do
            translated_info = translated_info:gsub(en, zh)
        end

        table.insert(translated, translated_info)
    end
    return translated
end

-- 翻译函数
function M.translate_item_name(item_name, translations)
    if not item_name or not translations then return item_name end

    -- 规则1: 处理带数字的情况，如 "1000 Endo"
    local number_prefix = item_name:match("^(%d+%s+)")
    local name_without_number = item_name
    if number_prefix then
        name_without_number = item_name:sub(#number_prefix + 1)
    end

    -- 规则2: 处理蓝图类型
    local blueprint_map = {
        ["Blueprint"] = "蓝图",
        ["Neuroptics Blueprint"] = "头部神经光元蓝图",
        ["Chassis Blueprint"] = "机体蓝图",
        ["Systems Blueprint"] = "系统蓝图"
    }

    -- 规则3: 处理遗物等级
    local relic_tier_map = {
        ["Lith"] = "古纪",
        ["Meso"] = "前纪",
        ["Neo"] = "中纪",
        ["Axi"] = "后纪",
        ["Requiem"] = "安魂"
    }

    -- 规则4: 处理遗物状态
    local relic_state_map = {
        ["Intact"] = "完整",
        ["Exceptional"] = "优良",
        ["Flawless"] = "无暇",
        ["Radiant"] = "光辉"
    }

    -- 规则5: 先尝试整体翻译
    local translated = translations[name_without_number]
    if translated then return (number_prefix or "") .. translated end

    -- 按空格分割单词逐个翻译
    local words = {}
    for word in name_without_number:gmatch("%S+") do
        -- 先检查蓝图映射
        local blueprint_translated = blueprint_map[word]
        if blueprint_translated then
            table.insert(words, blueprint_translated)
            -- 检查遗物等级映射
        elseif relic_tier_map[word] then
            table.insert(words, relic_tier_map[word])
            -- 检查遗物状态映射
        elseif relic_state_map[word] then
            table.insert(words, relic_state_map[word])
        else
            -- 查找翻译字典
            local word_translated = translations[word] or word
            table.insert(words, word_translated)
        end
    end

    return (number_prefix or "") .. table.concat(words, " ")
end

-- 翻译所有物品名称
function M.translate_all_items(item_drops, callback)
    json_reader.read_async("assets/json/wiki_translations.json",
                           function(err, translation_data)
        if err then
            callback(err, nil)
            return
        end

        local translations = translation_data.Text or {}
        local translated_drops = {}

        for item_name, drop_list in pairs(item_drops) do
            local translated_name = M.translate_item_name(item_name,
                                                          translations)
            translated_drops[translated_name] = drop_list -- 保持原始掉落信息
        end

        callback(nil, translated_drops)
    end)
end

-- 格式化missionRewards
function M.process_mission_rewards(mission_data)
    local item_drops = {}

    for planet, planet_data in pairs(mission_data) do
        for mission, mission_info in pairs(planet_data) do
            local game_mode = mission_info.gameMode or "Unknown"
            local location = planet .. "/" .. mission .. " (" .. game_mode ..
                                 ")"

            if mission_info.rewards then
                for tier, tier_rewards in pairs(mission_info.rewards) do
                    for _, reward in ipairs(tier_rewards) do
                        local item_name = reward.itemName
                        local rarity = reward.rarity or "Unknown"
                        local chance = reward.chance or 0

                        local drop_info =
                            location .. " | " .. rarity .. " | " .. chance ..
                                "%"

                        if not item_drops[item_name] then
                            item_drops[item_name] = {}
                        end
                        table.insert(item_drops[item_name], drop_info)
                    end
                end
            end
        end
    end

    return item_drops
end

function M.process_relics(relics_data)
    local item_drops = {}

    for _, relic in ipairs(relics_data) do
        local tier = relic.tier or "Unknown"
        local relic_name = relic.relicName or "Unknown"
        local state = relic.state or "Intact"

        local relic_info = tier .. "-" .. relic_name .. " (" .. state .. ")"

        if relic.rewards then
            for _, reward in ipairs(relic.rewards) do
                local item_name = reward.itemName
                local rarity = reward.rarity or "Unknown"
                local chance = reward.chance or 0

                local drop_info = relic_info .. " | " .. rarity .. " | " ..
                                      chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_transient_rewards(transient_data)
    local item_drops = {}

    for _, objective in ipairs(transient_data) do
        local objective_name = objective.objectiveName or "Unknown"

        if objective.rewards then
            for _, reward in ipairs(objective.rewards) do
                local rotation = reward.rotation or "Unknown"
                local item_name = reward.itemName
                local rarity = reward.rarity or "Unknown"
                local chance = reward.chance or 0

                local drop_info = objective_name .. "-rotation " .. rotation ..
                                      " | " .. rarity .. " | " .. chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_mod_locations(mod_data)
    local item_drops = {}

    for _, mod in ipairs(mod_data) do
        local item_name = mod.modName

        if mod.enemies then
            for _, enemy in ipairs(mod.enemies) do
                local enemy_name = enemy.enemyName or "Unknown"
                local rarity = enemy.rarity or "Unknown"
                local chance = enemy.chance or 0

                local drop_info = enemy_name .. " | " .. rarity .. " | " ..
                                      chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_enemy_mod_tables(enemy_data)
    local item_drops = {}

    for _, enemy in ipairs(enemy_data) do
        local enemy_name = enemy.enemyName or "Unknown"

        if enemy.mods then
            for _, mod in ipairs(enemy.mods) do
                local item_name = mod.modName
                local rarity = mod.rarity or "Unknown"
                local chance = mod.chance or 0

                local drop_info = enemy_name .. " | " .. rarity .. " | " ..
                                      chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_blueprint_locations(blueprint_data)
    local item_drops = {}

    for _, blueprint in ipairs(blueprint_data) do
        local item_name = blueprint.itemName

        if blueprint.enemies then
            for _, enemy in ipairs(blueprint.enemies) do
                local enemy_name = enemy.enemyName or "Unknown"
                local rarity = enemy.rarity or "Unknown"
                local chance = enemy.chance or 0

                local drop_info = enemy_name .. " | " .. rarity .. " | " ..
                                      chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_enemy_blueprint_tables(enemy_data)
    local item_drops = {}

    for _, enemy in ipairs(enemy_data) do
        local enemy_name = enemy.enemyName or "Unknown"

        if enemy.items then
            for _, item in ipairs(enemy.items) do
                local item_name = item.itemName
                local rarity = item.rarity or "Unknown"
                local chance = item.chance or 0

                local drop_info = enemy_name .. " | " .. rarity .. " | " ..
                                      chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_sortie_rewards(sortie_data)
    local item_drops = {}

    for _, reward in ipairs(sortie_data) do
        local item_name = reward.itemName
        local rarity = reward.rarity or "Unknown"
        local chance = reward.chance or 0

        local drop_info = "突击 | " .. rarity .. " | " .. chance .. "%"

        if not item_drops[item_name] then item_drops[item_name] = {} end
        table.insert(item_drops[item_name], drop_info)
    end

    return item_drops
end

function M.process_key_rewards(key_data)
    local item_drops = {}

    for _, key in ipairs(key_data) do
        local key_name = key.keyName or "Unknown"

        if key.rewards then
            for rotation, rotation_rewards in pairs(key.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info = key_name .. " | " .. rotation .. " | " ..
                                          rarity .. " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_cetus_bounty_rewards(bounty_data)
    local item_drops = {}

    for _, bounty in ipairs(bounty_data) do
        local bounty_level = bounty.bountyLevel or "Unknown"

        if bounty.rewards then
            for rotation, rotation_rewards in pairs(bounty.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info =
                        bounty_level .. " | " .. rotation .. " | " .. rarity ..
                            " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_solaris_bounty_rewards(bounty_data)
    local item_drops = {}

    for _, bounty in ipairs(bounty_data) do
        local bounty_level = bounty.bountyLevel or "Unknown"

        if bounty.rewards then
            for rotation, rotation_rewards in pairs(bounty.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info =
                        bounty_level .. " | " .. rotation .. " | " .. rarity ..
                            " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_deimos_bounty_rewards(bounty_data)
    local item_drops = {}

    for _, bounty in ipairs(bounty_data) do
        local bounty_level = bounty.bountyLevel or "Unknown"

        if bounty.rewards then
            for rotation, rotation_rewards in pairs(bounty.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info =
                        bounty_level .. " | " .. rotation .. " | " .. rarity ..
                            " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_zariman_bounty_rewards(bounty_data)
    local item_drops = {}

    for _, bounty in ipairs(bounty_data) do
        local bounty_level = bounty.bountyLevel or "Unknown"

        if bounty.rewards then
            for rotation, rotation_rewards in pairs(bounty.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info =
                        bounty_level .. " | " .. rotation .. " | " .. rarity ..
                            " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_entrati_lab_rewards(bounty_data)
    local item_drops = {}

    for _, bounty in ipairs(bounty_data) do
        local bounty_level = bounty.bountyLevel or "Unknown"

        if bounty.rewards then
            for rotation, rotation_rewards in pairs(bounty.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info =
                        bounty_level .. " | " .. rotation .. " | " .. rarity ..
                            " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_hex_rewards(bounty_data)
    local item_drops = {}

    for _, bounty in ipairs(bounty_data) do
        local bounty_level = bounty.bountyLevel or "Unknown"

        if bounty.rewards then
            for rotation, rotation_rewards in pairs(bounty.rewards) do
                for _, reward in ipairs(rotation_rewards) do
                    local item_name = reward.itemName
                    local rarity = reward.rarity or "Unknown"
                    local chance = reward.chance or 0

                    local drop_info =
                        bounty_level .. " | " .. rotation .. " | " .. rarity ..
                            " | " .. chance .. "%"

                    if not item_drops[item_name] then
                        item_drops[item_name] = {}
                    end
                    table.insert(item_drops[item_name], drop_info)
                end
            end
        end
    end

    return item_drops
end

function M.process_syndicates(syndicates_data)
    local item_drops = {}

    for syndicate_name, items in pairs(syndicates_data) do
        for _, item_data in ipairs(items) do
            local item_name = item_data.item
            local rarity = item_data.rarity or "Unknown"
            local chance = item_data.chance or 0
            local place = item_data.place or "Unknown"

            local drop_info = place .. " | " .. rarity .. " | " .. chance .. "%"

            if not item_drops[item_name] then
                item_drops[item_name] = {}
            end
            table.insert(item_drops[item_name], drop_info)
        end
    end

    return item_drops
end

function M.process_resource_by_avatar(avatar_data)
    local item_drops = {}

    for _, avatar in ipairs(avatar_data) do
        local source = avatar.source or "Unknown"

        if avatar.items then
            for _, item in ipairs(avatar.items) do
                local item_name = item.item
                local rarity = item.rarity or "Unknown"
                local chance = item.chance or 0

                local drop_info =
                    source .. " | " .. rarity .. " | " .. chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_sigil_by_avatar(avatar_data)
    local item_drops = {}

    for _, avatar in ipairs(avatar_data) do
        local source = avatar.source or "Unknown"

        if avatar.items then
            for _, item in ipairs(avatar.items) do
                local item_name = item.item
                local rarity = item.rarity or "Unknown"
                local chance = item.chance or 0

                local drop_info =
                    source .. " | " .. rarity .. " | " .. chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

function M.process_additional_item_by_avatar(avatar_data)
    local item_drops = {}

    for _, avatar in ipairs(avatar_data) do
        local source = avatar.source or "Unknown"

        if avatar.items then
            for _, item in ipairs(avatar.items) do
                local item_name = item.item
                local rarity = item.rarity or "Unknown"
                local chance = item.chance or 0

                local drop_info =
                    source .. " | " .. rarity .. " | " .. chance .. "%"

                if not item_drops[item_name] then
                    item_drops[item_name] = {}
                end
                table.insert(item_drops[item_name], drop_info)
            end
        end
    end

    return item_drops
end

-- 通用合并函数
function M.merge_data(target, source)
    for item_name, drop_list in pairs(source) do
        if not target[item_name] then target[item_name] = {} end
        for _, drop_info in ipairs(drop_list) do
            table.insert(target[item_name], drop_info)
        end
    end
end

-- 处理所有掉落数据的主函数
function M.process_all_drops(data, callback)
    local d_hash = {}
    for k, v in pairs(data) do d_hash[k] = v end

    local processed_data = M.process_mission_rewards(d_hash["missionRewards"])

    local data_sources = {
        M.process_relics(d_hash["relics"]),
        M.process_transient_rewards(d_hash["transientRewards"]),
        M.process_mod_locations(d_hash["modLocations"]),
        M.process_enemy_mod_tables(d_hash["enemyModTables"]),
        M.process_blueprint_locations(d_hash["blueprintLocations"]),
        M.process_enemy_blueprint_tables(d_hash["enemyBlueprintTables"]),
        M.process_sortie_rewards(d_hash["sortieRewards"]),
        M.process_key_rewards(d_hash["keyRewards"]),
        M.process_cetus_bounty_rewards(d_hash["cetusBountyRewards"]),
        M.process_solaris_bounty_rewards(d_hash["solarisBountyRewards"]),
        M.process_deimos_bounty_rewards(d_hash["deimosRewards"]),
        M.process_zariman_bounty_rewards(d_hash["zarimanRewards"]),
        M.process_entrati_lab_rewards(d_hash["entratiLabRewards"]),
        M.process_hex_rewards(d_hash["hexRewards"]),
        M.process_syndicates(d_hash["syndicates"]),
        M.process_resource_by_avatar(d_hash["resourceByAvatar"]),
        M.process_sigil_by_avatar(d_hash["sigilByAvatar"]),
        M.process_additional_item_by_avatar(d_hash["additionalItemByAvatar"])
    }

    for _, source in ipairs(data_sources) do
        M.merge_data(processed_data, source)
    end

    -- 翻译物品名称并创建双索引
    M.translate_all_items(processed_data, function(err, translated_drops)
        if err then
            callback(err, nil, nil)
            return
        end

        -- 创建英文索引（原始数据）
        local english_drops = processed_data

        callback(nil, translated_drops, english_drops)
    end)
end

return M
