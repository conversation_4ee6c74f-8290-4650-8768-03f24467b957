-- core/bot.lua
local json = require('json')
local formatter = require('./message')
local api = require "./api"

local bot = {}
-- bot的元表，__index指向bot自身，这样新创建的实例可以访问bot里的方法
bot.__index = bot


function bot.new(conn)
    -- setmetatable的第二个参数应该是bot的元表，即bot自身
    local self = setmetatable({}, bot)
    self.conn = conn
    return self
end

-- 底层发送函数
function bot:_send(data)
    local payload = {
        action = data.action,
        params = data.params
    }
    -- 下面两行代码已存在，保持即可
    formatter.format_sent(data.action, data.params)
    self.conn:send(json.encode(payload))
end

-- 将onebot里的所有api绑定到bot上
for method_name, method_func in pairs(api) do
    bot[method_name] = method_func
end


return bot
