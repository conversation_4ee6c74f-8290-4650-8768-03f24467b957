-- core/dispatcher.lua
local log = require('./log')
local Bot = require('./bot')
local fs = require('fs')
local matcher_module = require('./matcher')


local bot_instance = nil
local dispatcher = {}
-- local plugins = {}
local plugins_dir = "plugins"
-------------------------

-- 已加载的库缓存
local loaded_libs = {}

-- Luvit内置库列表
local luvit_libs = {
    'buffer', 'childprocess', 'codec', 'core',
    'dgram', 'dns', 'fs', 'helpful', 'hooks', 'http-codec', 'http',
    'https', 'json', 'los', 'net', 'pretty-print',
    'querystring', 'readline', 'timer', 'url', 'utils',
    'stream', 'tls', 'path'
}

-- 检查表中是否包含某个值
local function contains(list, value)
    for _, v in ipairs(list) do
        if v == value then
            return true
        end
    end
    return false
end

-- 创建延迟加载器
local function create_lazy_loader()
    local loader = {}

    -- 设置元表实现延迟加载
    local mt = {
        __index = function(_, lib_name)
            -- 检查是否已加载
            if not loaded_libs[lib_name] then
                log.debug("延迟加载Luvit库: " .. lib_name)
                local ok, lib = pcall(require, lib_name)
                if ok then
                    loaded_libs[lib_name] = lib
                else
                    log.warn("无法加载Luvit库: " .. lib_name .. ", 错误: " .. tostring(lib))
                    loaded_libs[lib_name] = {} -- 防止重复尝试加载失败的库
                end
            end
            return loaded_libs[lib_name]
        end
    }

    setmetatable(loader, mt)
    return loader
end

-- 使用延迟加载器加载模块
local function load_module_with_lazy_libs(module_path, lazy_loader)
    -- 保存原始require函数
    local original_require = require

    -- 重写require函数，拦截对内置库的请求
    _G.require = function(name)
        if contains(luvit_libs, name) then
            return lazy_loader[name]
        else
            return original_require(name)
        end
    end

    -- 加载模块
    local ok, result = pcall(original_require, module_path)

    -- 恢复原始require函数
    _G.require = original_require

    if not ok then
        return false, result
    end

    return true, result
end


-- 加载单个插件
local function load_plugin(plugin_path, display_name, lazy_loader)
    local load_ok, plugin_or_err = load_module_with_lazy_libs(plugin_path, lazy_loader)

    if load_ok then
        log.info("已加载插件: " .. display_name)
        return true
    else
        log.error("加载插件失败: " .. display_name)
        log.error("错误原因: " .. tostring(plugin_or_err))
        return false
    end
end

function dispatcher.load_plugins()
    log.info("--- 开始加载插件 ---")

    -- 创建延迟加载器
    local lazy_loader = create_lazy_loader()
    log.info("已启用Luvit库延迟加载机制")

    local ok, files_or_err = pcall(fs.readdirSync, plugins_dir)
    --错误处理
    if not ok then
        log.error("无法读取插件目录 '" .. plugins_dir .. "'")
        log.error("原始错误: " .. tostring(files_or_err))
        log.error("--- 插件加载失败 ---")
        return
    end

    local files = files_or_err
    if #files == 0 then
        log.warn("插件目录为空，未加载任何插件")
        return
    end

    local loaded_count = 0

    for _, filename in ipairs(files) do
        local full_path = plugins_dir .. "/" .. filename
        local stat_ok, stat = pcall(fs.statSync, full_path)

        if stat_ok and stat.type == "file" and filename:match("%.lua$") then
            -- 最外层 .lua 文件
            local module_path = plugins_dir .. "/" .. filename:gsub("%.lua$", "")
            if load_plugin(module_path, filename, lazy_loader) then
                loaded_count = loaded_count + 1
            end
        elseif stat_ok and stat.type == "directory" then
            -- 目录形式的插件，查找 init.lua
            local init_path = full_path .. "/init.lua"
            local init_stat_ok, init_stat = pcall(fs.statSync, init_path)

            if init_stat_ok and init_stat.type == "file" then
                local module_path = plugins_dir .. "/" .. filename .. "/init"
                if load_plugin(module_path, filename .. "/init.lua", lazy_loader) then
                    loaded_count = loaded_count + 1
                end
            else
                log.warn("目录插件缺少入口文件: " .. filename .. "/init.lua")
            end
        end
    end

    log.info("--- 插件加载完毕，共加载 " .. loaded_count .. " 个插件 ---")
end

function dispatcher.handle(event, conn)
    -- 只处理群消息
    if event.post_type == "message" and event.message_type ~= "group" then
        return
    end
    
    bot_instance = bot_instance or Bot.new(conn)
    bot_instance.conn = conn
    local bot = bot_instance
    local matchers = matcher_module.get_matchers()
    
    -- 按优先级排序
    table.sort(matchers, function(a, b) return a.priority < b.priority end)
    
    for _, m in ipairs(matchers) do
        if (m.event_type == "" or m.event_type == event.post_type) then
            if not m.rule or m.rule(event) then
                local ok, result = pcall(m.handler, bot, event)
                if not ok then
                    log.error("处理器执行失败: " .. tostring(result))
                end
                
                if m.temp then
                    -- 移除临时匹配器
                    for i, matcher in ipairs(matchers) do
                        if matcher == m then
                            table.remove(matchers, i)
                            break
                        end
                    end
                end
                
                if m.block then break end
            end
        end
    end
end

return dispatcher



