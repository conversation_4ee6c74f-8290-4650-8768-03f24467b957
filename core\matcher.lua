-- core/matcher.lua
local matcher = {}
local matchers = {}

-- 参数分割函数
local function split_args(args, separator)
    if not args or args == "" then return {} end
    
    separator = separator or "%s+"  -- 默认按空格分割
    local result = {}
    
    -- 处理逗号分隔符
    if separator == "," then
        for arg in args:gmatch("([^,]+)") do
            table.insert(result, arg:match("^%s*(.-)%s*$"))  -- 去除首尾空格
        end
    else
        -- 处理空格分隔符
        for arg in args:gmatch("%S+") do
            table.insert(result, arg)
        end
    end
    
    return result
end

-- 创建匹配器
function matcher.new(event_type, rule_func, handler_func, options)
    options = options or {}
    local m = {
        event_type = event_type or "",
        rule = rule_func,
        handler = handler_func,
        priority = options.priority or 1,
        block = options.block or false,
        temp = options.temp or false
    }
    table.insert(matchers, m)
    return m
end

-- 基础事件响应器
function matcher.on(event_type, rule_func, handler_func, options)
    return matcher.new(event_type, rule_func, handler_func, options)
end

-- 消息事件响应器
function matcher.on_message(rule_func, handler_func, options)
    options = options or {}
    options.block = options.block ~= false -- 默认阻断
    return matcher.on("message", rule_func, handler_func, options)
end

-- 命令响应器
function matcher.on_command(cmd, handler_func, options)
    options = options or {}
    local force_whitespace = options.force_whitespace
    local split_by = options.split_by
    local aliases = options.aliases or {}  -- 新增别名支持
    
    -- 构建所有可能的命令列表
    local commands = {cmd}
    for _, alias in ipairs(aliases) do
        table.insert(commands, alias)
    end
    
    local rule = function(event)
        if not event.raw_message then return false end
        local msg = event.raw_message
        
        -- 检查所有命令和别名
        for _, command in ipairs(commands) do
            local match = false
            if force_whitespace == true then
                match = msg:match("^" .. command .. "%s") ~= nil
            elseif force_whitespace == false then
                match = msg:match("^" .. command) ~= nil
            else
                match = msg:match("^" .. command .. "$") or msg:match("^" .. command .. "%s")
            end
            
            if match then
                return true, command  -- 返回匹配的命令
            end
        end
        return false
    end
    
    -- 包装处理函数，提取参数时使用实际匹配的命令
    local wrapped_handler = function(bot, event)
        if event.raw_message then
            local msg = event.raw_message
            local matched_cmd = nil
            
            -- 找到实际匹配的命令
            for _, command in ipairs(commands) do
                if force_whitespace == true then
                    if msg:match("^" .. command .. "%s") then
                        matched_cmd = command
                        break
                    end
                elseif force_whitespace == false then
                    if msg:match("^" .. command) then
                        matched_cmd = command
                        break
                    end
                else
                    if msg:match("^" .. command .. "$") or msg:match("^" .. command .. "%s") then
                        matched_cmd = command
                        break
                    end
                end
            end
            
            -- 提取参数
            local args = ""
            if matched_cmd then
                if force_whitespace == true then
                    args = msg:match("^" .. matched_cmd .. "%s+(.*)") or ""
                elseif force_whitespace == false then
                    args = msg:match("^" .. matched_cmd .. "%s*(.*)") or ""
                else
                    args = msg:match("^" .. matched_cmd .. "%s+(.*)") or ""
                end
            end
            
            event.args = args
            event.matched_command = matched_cmd  -- 新增：记录匹配的命令
            
            if split_by then
                event.args_list = split_args(args, split_by)
            end
        end
        
        return handler_func(bot, event)
    end
    
    return matcher.on_message(rule, wrapped_handler, options)
end

-- 关键词响应器
function matcher.on_keyword(keywords, handler_func, options)
    local rule = function(event)
        if not event.raw_message then return false end
        for _, keyword in ipairs(keywords) do
            if event.raw_message:find(keyword) then return true end
        end
        return false
    end
    return matcher.on_message(rule, handler_func, options)
end

-- 获取所有匹配器
function matcher.get_matchers()
    return matchers
end

return matcher



