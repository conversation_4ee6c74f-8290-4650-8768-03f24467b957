-- core/ws-server.lua
local log = require('./log')
local ws_library = require('websocket')
local json = require('json')
local formatter = require('./message')
local dispatcher = require('./dispatcher')
local timer = require('timer')

local server_module = {}
local connected_clients = 0

local function check_connection()
    if connected_clients == 0 then
        log.warn("正在等待客户端连接...")
    end
end

function server_module.start(port)
    local server = ws_library.server.new()

    server:on("connect", function()
        connected_clients = connected_clients + 1
        -- log.info("客户端已连接! 当前连接数:", connected_clients)
    end)

    server:on("disconnect", function()
        connected_clients = connected_clients - 1
        -- log.info("客户端已断开! 当前连接数:", connected_clients)
    end)

    server:on("data", function(client, message)
        local ok, data = pcall(json.decode, message)

        if ok then
            formatter.format_received(data)
            dispatcher.handle(data, client)
        else
            log.error("收到的不是一个有效的 JSON 消息:", message)
        end
    end)

    server:listen(port, function()
        log.info("WebSocket 服务器已启动，正在监听端口:", port)

        -- 5秒后首次检查，然后启动定时器
        timer.setTimeout(5000, function()
            check_connection()
            -- 首次检查后，每30秒检查一次
            timer.setInterval(30000, check_connection)
        end)
    end)

    return server
end

return server_module
