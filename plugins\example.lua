
local matcher = require('../core/matcher')



-- function plugin.on_message(bot, event)
--     if event.raw_message == "你好." then
--         bot:send_group_msg(event.group_id, "你好呀")
--     end
-- end



-- 1. 基础命令：/help
matcher.on_command("/help", function(bot, event)
    if event.args and event.args ~= "" then
        bot:send_group_msg(event.group_id, "帮助主题：" .. event.args)
    else
        bot:send_group_msg(event.group_id, "可用命令：/help, /ban, /add, /say")
    end
end)

-- 2. 必须有空格的命令：天气 北京
matcher.on_command("天气", function(bot, event)
    local city = event.args or "未指定城市"
    bot:send_group_msg(event.group_id, "🌤️ 查询天气：" .. city)
end, {force_whitespace = true})

-- 3. 可以直接跟内容：查询北京 或 查询 北京
matcher.on_command("查询", function(bot, event)
    local content = event.args or "请输入查询内容"
    bot:send_group_msg(event.group_id, "🔍 查询：" .. content)
end, {force_whitespace = false})

-- 4. 按空格分割参数：/ban 123 456 789
matcher.on_command("/ban", function(bot, event)
    if event.args_list and #event.args_list > 0 then
        local users = table.concat(event.args_list, ", ")
        bot:send_group_msg(event.group_id, "🚫 封禁用户：" .. users)
    else
        bot:send_group_msg(event.group_id, "请指定用户ID，用空格分隔")
    end
end, {split_by = " "})

-- 5. 按逗号分割参数：/add 苹果,香蕉,橘子
matcher.on_command("/add", function(bot, event)
    if event.args_list and #event.args_list > 0 then
        for _, item in ipairs(event.args_list) do
            bot:send_group_msg(event.group_id, "➕ 添加物品：" .. item)
        end
    else
        bot:send_group_msg(event.group_id, "请指定物品，用逗号分隔")
    end
end, {split_by = ","})

-- 6. 直接说话：/say 你好世界
matcher.on_command("/say", function(bot, event)
    local content = event.args or "没有内容"
    bot:send_group_msg(event.group_id, "💬 " .. content)
end)

-- 7. 关键词响应
matcher.on_keyword({"/结合"}, function(bot, event)
    bot:send_group_msg(event.group_id, "🔗 检测到合成关键词")
end)

-- 8. 命令别名示例：/info 或 /查看 或 /详情
matcher.on_command("/info", function(bot, event)
    local content = event.args or "默认信息"
    bot:send_group_msg(event.group_id, "ℹ️ 信息：" .. content .. " (使用命令：" .. event.matched_command .. ")")
end, {aliases = {"/查看", "/详情"}})

-- return plugin

