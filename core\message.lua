-- core/message-formatter.lua
local log = require('./log')

-- HTML实体解码函数
local function decode_html_entities(text)
    if not text then return text end
    return text:gsub("&amp;", "&")
              :gsub("&lt;", "<")
              :gsub("&gt;", ">")
              :gsub("&quot;", '"')
              :gsub("&#39;", "'")
end

local formatter = {}

function formatter.format_received(event)
    local post_type = event.post_type
    
    if post_type == "meta_event" then
        if event.meta_event_type == "heartbeat" then
            log.debug("💓 心跳包")
        elseif event.meta_event_type == "lifecycle" and event.sub_type == "connect" then
            log.info("🤖 机器人连接: " .. tostring(event.self_id))
        end
    elseif post_type == "message" then
        -- 解码HTML实体
        if event.raw_message then
            event.raw_message = decode_html_entities(event.raw_message)
        end
        
        local sender = event.sender and event.sender.nickname or "未知用户"
        if event.message_type == "private" then
            log.info("📩 私聊消息 [" .. sender .. "(" .. event.user_id .. ")]: " .. event.raw_message)
        elseif event.message_type == "group" then
            log.info("💬 群消息 [" .. (event.group_id or "未知群") .. "] " .. sender .. ": " .. event.raw_message)
        end
    elseif post_type == "notice" then
        log.info("📢 通知事件: " .. (event.notice_type or "未知"))
    elseif post_type == "request" then
        log.info("🔔 请求事件: " .. (event.request_type or "未知"))
    end
end

function formatter.format_sent(action, params)
    if action == "send_private_msg" then
        log.info("📤 发送私聊消息 -> " .. params.user_id .. ": " .. params.message)
    elseif action == "send_group_msg" then
        log.info("📤 发送群消息 -> " .. params.group_id .. ": " .. params.message)
    else
        log.debug("📤 发送API请求: " .. action)
    end
end

return formatter
