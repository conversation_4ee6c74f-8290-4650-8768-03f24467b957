local matcher = require('../core/matcher')
local json_reader = require('../utils/json_reader')
local log = require "../core/log"



matcher.on_command("结合", function(bot, event)
    json_reader.read_async("assets/json/jiehe.json", function(err, data)
        if err then
            log.info("读取文件出错:", err)
            return
        end

        local content = event.args or ""
        if content == "" then
            bot:send_group_msg(event.group_id, "请输入要查询的物品名称")
            return
        end

        -- 查找匹配的物品
        for _, item in ipairs(data) do
            if item.name_zh == content then
                local locations = table.concat(item.locations, "\n")
                bot:send_group_msg(event.group_id, content .. " 的出现位置：\n" .. locations)
                return
            end
        end

        -- 未找到匹配项
        bot:send_group_msg(event.group_id, "未找到 \"" .. content .. "\" 的相关信息")
    end)
end)


